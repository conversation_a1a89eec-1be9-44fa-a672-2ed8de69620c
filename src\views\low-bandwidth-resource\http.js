import { ajax } from "@/api/ajax";
import { formPromise } from "@/utils/fetch";

const prefix = "api/v1"
export default {
  async getLowBandwidthResourceList(params) {
    return await formPromise(ajax.post(`${prefix}/sda/low_bw_resource/get`, params))
  },
  // 获取大区参数
  async getAreaParams() {
    return await formPromise(ajax.get(`${prefix}/sda/low_bw_resource/param/area`))
  },
  // 获取省份参数
  async getProvinceParams() {
    return await formPromise(ajax.get(`${prefix}/sda/low_bw_resource/param/province`))
  },
  // 获取模块名称参数
  async getModuleNameParams() {
    return await formPromise(ajax.get(`${prefix}/sda/low_bw_resource/param/module_name`))
  },
  // 获取供应商参数
  async getSupplierParams() {
    return await formPromise(ajax.get(`${prefix}/sda/low_bw_resource/param/supplier`))
  },
  // 获取运营商参数
  async getIspParams() {
    return await formPromise(ajax.get(`${prefix}/sda/low_bw_resource/param/isp`))
  },
  // 查询设备验收信息列表
  async queryDeviceAcceptanceList(params) {
    return await formPromise(ajax.post(`${prefix}/sda/device_acceptance/query`, params));
  },
  // 更新设备验收信息
  async updateDeviceAcceptanceInfo(params) {
    return await formPromise(ajax.post(`${prefix}/sda/device_acceptance/update_acceptance_info`, params));
  },
  // 批量交维操作
  async commitDevices(params) {
    return await formPromise(ajax.post(`${prefix}/sda/device_acceptance/commit`, params));
  },
  // 获取所有供应商
  async getAllSuppliers() {
    return await formPromise(ajax.get(`${prefix}/sda/supplier/simple_list`));
  },
  // 批量导出
  async batchExport(params) {
    return await formPromise(ajax.post(`${prefix}/sda/low_bw_resource/export`, params, {
      responseType: 'blob'  // 设置响应类型为blob，用于文件下载
    }));
  }
}
