<template>
  <el-card class="node-bandwidth-source-config">
    <div class="search-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="IP">
          <el-input v-model="searchForm.ip" style='width: 150px;'></el-input>
        </el-form-item>
        <el-form-item label="CDN类型" prop="cdn_type">
          <el-select v-model="searchForm.cdn_type" placeholder="请选择CDN类型" style='width: 150px;'>
            <el-option v-for="item in cdnTypeFilterList" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchData">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="primary" @click="showAddDialog">新增</el-button>
          <el-button type="primary" @click="showAddDialog">批量修改</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="tableData" style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="node_id_str" label="NodeId"></el-table-column>
      <el-table-column prop="node_name" label="Node名称"></el-table-column>
      <el-table-column prop="lake_names" label="关联的Lake"></el-table-column>
      <el-table-column prop="operator" label="操作人"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)">修改</el-button>
          <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>

    <!-- 新增/编辑弹窗 -->
    <el-dialog append-to-body :title="dialogTitle" :visible.sync="dialogVisible" :show-close="false" width="680px">
      <el-form :model="form" :rules="rules" ref="form" label-width="80px">
        <el-col :span="24">
          <el-form-item label="IP" prop="ips">
            <el-select v-model="form.ips" placeholder="请选择IP" filterable :disabled="isEdit" style="width: 90%"
              @change="handleNodeChange">
              <el-option v-for="item in ipOptions" :key="item.ip" :label="item.ip" :value="item.ip">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="CDN类型" prop="cdn_type">
            <el-select v-model="form.cdn_type" placeholder="请选择CDN类型" style="width: 90%">
              <el-option v-for="item in cdnTypeFilterList" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form :model="form" label-width="auto" :inline="true" ref="form">
            <el-form-item label="标签键:  ">
              <el-select placeholder="请选择" style="width: 100%" v-model="form.tag_key">
                <el-option v-for="item in tagList" :label="item.tag_key" :value="item.tag_key" :key="item.tag_key">{{
                  item.tag_key }}
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="标签值: ">
              <el-select placeholder="请选择" style="width: 100%" v-model="form.tag_value">
                <el-option v-for="item in tagValueOptions" :label="item.value" :value="item.value" :key="item.value">{{
                  item.value }}
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <!-- <el-button style="float: right" @click="addHostTag" :disabled="isLoading" type="primary"
                icon="el-icon-plus" size="mini">添加</el-button> -->
            </el-form-item>
          </el-form>
        </el-col>
        <el-table :data="hostTagList" tooltip-effect="dark" style="width: 100%">
          <el-table-column type="index" label="序号" width="55"></el-table-column>
          <el-table-column label="标签键" prop="tag_key"></el-table-column>
          <el-table-column label="标签值" prop="tag_value">
          </el-table-column>
          <el-table-column width="100" label="操作">
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" content="删除" placement="top">
                <el-button icon="el-icon-delete" type="danger" circle size="mini"
                  @click="removeVip(scope.row)"></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 删除确认框 -->
    <el-dialog append-to-body title="提示" :visible.sync="deleteDialogVisible" width="400px">
      <div>确定要删除该配置吗？</div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete" :loading="deleteLoading">确 定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import http from "../http";
import { mapState } from "vuex";
import bizGroupMinxin from '@/views/host-tag-config/bizGroupMinxin.js'
export default {
  name: "NodeBandwidthSourceConfig",
  mixins: [bizGroupMinxin],
  data() {
    return {
      hostTagList: [],
      tagList: [],
      ipList: [],
      // 表格数据
      tableData: [],
      loading: false,
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 搜索
      searchForm: {
        node_name: "",
        lake_name: "",
      },
      // 弹窗
      dialogVisible: false,
      dialogTitle: "新增主机标签配置",
      isEdit: false,
      submitLoading: false,
      form: {
        id: "",
        node_id_str: "",
        node_name: "",
        bw_source: 0,
      },
      rules: {
        ips: [
          { required: true, message: "ips", trigger: "change" },
        ],
        cdn_type: [
          { required: true, message: "ips", trigger: "change" },
        ],
        bw_source: [
          { required: true, message: "请选择数据源", trigger: "change" },
        ],
      },
      // 删除确认框
      deleteDialogVisible: false,
      deleteLoading: false,
      deleteId: null,
      tagList: [],
    };

  },
  watch: {
    'form.tag_key': {
      handler(newVal) {
        this.form.tag_value = ''
        if (newVal) {
          this.tagValueOptions = this.getTagValueList(newVal);
        } else {
          this.tagValueOptions = [];
        }
      },
      immediate: true, // 立即执行一次
    },
  },
  created() {
    // Dispatch action to load nodeList data
    this.fetchData();
    this.getTagList();
    this.getIpList();
  },
  computed: {
    ...mapState({
      nodeList: (state) => state.baseData.nodeList,
      lakeList: (state) => state.baseData.fullLakeList,
    }),
    ipOptions() {
      return this.ipList.map((itm) => {
        return {
          ip: itm.ip,
          supplier_uuid: itm.supplier_uuid,
        };
      });
    },
    tagOptions() {
      return this.tagList.map((itm) => {
        return {
          tag_name: itm.tag_name,
          tag_key: itm.tag_key,
        };
      });
    },
    lakeOptions() {
      return this.lakeList.map((itm) => {
        return {
          lake_name: itm.bk_inst_name,
          lake_id: itm.bk_inst_id,
          lake_code: itm.node_code,
        };
      });
    },
    nodeOptions() {
      return this.nodeList.map((itm) => {
        return {
          node_name: itm.nodeName,
          node_id: itm.nodeId,
          node_id_str: itm.nodeIdStr,
        };
      });
    },
  },
  methods: {
    checkForm() {
      this.valueError = ''
      let flag = true
      let existIps = this.vipList.filter((item) => { return item.ip === this.form.ip })
      if (existIps && existIps.length > 0) {
        flag = false
        this.valueError = 'IP已存在'
      }
      return flag
    },
    getTagValueList(tagKey) {
      const tag = this.tagList.find(item => item.tag_key === tagKey);
      if (tag && tag.tag_value_list) {
        return tag.tag_value_list.map(item => ({
          value: item,
          label: item,
        }));
      }
      return [];
    },

    async getTagList() {
      this.loading = true;
      try {
        const res = await http.getTagList();
        if (res && res.data) {
          this.tagList = res.data.items || [];
        } else {
          this.tagList = [];
        }

        this.loading = false;
      } catch (error) {
        console.error(error);
        this.loading = false;
        this.$message.error("获取数据失败");
      }
    },
    async getIpList() {
      this.loading = true;
      try {
        const res = await http.getIpList();
        if (res && res.data) {
          this.ipList = res.data.items || [];
        } else {
          this.ipList = [];
        }
        this.loading = false;
      } catch (error) {
        console.error(error);
        this.loading = false;
        this.$message.error("获取数据失败");
      }
    },
    // 关闭弹窗
    handleCloseDialog() {
      this.dialogVisible = false;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleNodeChange() {
      const nodeOption = this.nodeOptions.find(
        (itm) => itm.node_id_str === this.form.node_id_str
      );
      if (!nodeOption) return;
      this.form.node_name = nodeOption.node_name;
    },
    // 获取表格数据
    async fetchData() {
      this.loading = true;
      try {
        const res = await http.getNodeBandwidthSourceList({
          page: this.currentPage,
          page_size: this.pageSize,
          node_name: this.searchForm.node_name,
          lake_name: this.searchForm.lake_name,
        });

        if (res && res.data) {
          this.tableData = res.data.items || [];
          this.total = res.data.total || 0;
        } else {
          this.tableData = [];
          this.total = 0;
        }

        this.loading = false;
      } catch (error) {
        console.error(error);
        this.loading = false;
        this.$message.error("获取数据失败");
      }
    },

    // 搜索
    searchData() {
      this.currentPage = 1;
      this.fetchData();
    },

    // 重置
    resetForm() {
      this.searchForm = {
        node_name: "",
        lake_name: "",
      };
      this.searchData();
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchData();
    },

    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchData();
    },

    // 新增
    showAddDialog() {
      this.dialogTitle = "新增主机标签配置";
      this.isEdit = false;
      this.form = {
        id: "",
        node_id: "",
        node_name: "",
        bw_source: 0,
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = "修改Node带宽数据源配置";
      this.isEdit = true;
      this.form = {
        id: row.id,
        node_id_str: row.node_id_str,
        node_name: row.node_name,
        bw_source: row.bw_source,
      };
      this.dialogVisible = true;
    },

    // 提交表单
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          let res;
          try {
            // 准备提交的数据
            const submitData = {
              id: this.form.id,
              node_id_str: this.form.node_id_str,
              node_name: this.form.node_name,
              bw_source: this.form.bw_source,
            };

            if (this.isEdit) {
              // 编辑
              res = await http.updateNodeBandwidthSource(submitData);
              if (!res) {
                this.submitLoading = false;
                return;
              }

              // 重新加载数据
              this.fetchData();
              this.$message.success("修改成功");
            } else {
              delete submitData.id;
              // 新增
              res = await http.addNodeBandwidthSource(submitData);
              if (!res) {
                this.submitLoading = false;
                return;
              }

              // 重新加载数据
              this.fetchData();
              this.$message.success("新增成功");
            }

            this.dialogVisible = false;
          } catch (error) {
            console.error(error);
          } finally {
            this.submitLoading = false;
          }
        } else {
          return false;
        }
      });
    },

    // 删除
    handleDelete(row) {
      this.deleteId = row.id;
      this.deleteDialogVisible = true;
    },

    // 确认删除
    async confirmDelete() {
      if (!this.deleteId) return;

      this.deleteLoading = true;
      try {
        // 调用删除API
        await http.deleteNodeBandwidthSource(this.deleteId);

        // 从表格移除
        const index = this.tableData.findIndex(
          (item) => item.id === this.deleteId
        );
        if (index !== -1) {
          this.tableData.splice(index, 1);
          this.total--;
        }

        this.$message.success("删除成功");
        this.deleteDialogVisible = false;
        this.deleteLoading = false;
      } catch (error) {
        console.error(error);
        this.deleteLoading = false;
        this.$message.error("删除失败");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.node-bandwidth-source-config {
  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
