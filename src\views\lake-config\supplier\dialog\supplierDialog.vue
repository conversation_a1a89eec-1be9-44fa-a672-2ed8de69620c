<template>
  <el-dialog :title="isEdit ? '修改供应商' : '新增供应商'" :visible.sync="dialogVisible" width="800px" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" @close="handleClose">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="供应商名称" prop="supplier_name">
        <el-input v-model.trim="form.supplier_name" :disabled="isEdit" placeholder="请输入供应商名称"></el-input>
      </el-form-item>
      <el-form-item label="供应商代码" prop="supplier_code">
        <el-input v-model.trim="form.supplier_code" placeholder="请输入供应商代码"></el-input>
      </el-form-item>
      <el-form-item label="群ID" prop="contact_group_id">
        <el-input v-model.trim="form.contact_group_id" placeholder="请输入群ID"></el-input>
      </el-form-item>
      <el-form-item label="群名称" prop="contact_group_name">
        <el-input v-model.trim="form.contact_group_name" placeholder="请输入群名称"></el-input>
      </el-form-item>
      <el-form-item label="联系人" prop="contact">
        <el-input v-model.trim="form.contact" placeholder="请输入联系人"></el-input>
      </el-form-item>
      <el-form-item label="联系人邮箱" prop="email">
        <el-input v-model.trim="form.email" placeholder="请输入联系人邮箱"></el-input>
      </el-form-item>
      <el-form-item label="联系人电话" prop="phone">
        <el-input v-model.trim="form.phone" placeholder="请输入联系人电话"></el-input>
      </el-form-item>
      <el-form-item label="IP段" prop="ips_list">
        <div class="ip-section">
          <div v-for="(item, index) in form.ips_list" :key="index" class="ip-item">
            <div class="ip-row">
              <el-input 
                v-model.trim="item.ips" 
                placeholder="请输入IP段，手动输入"
                style="width: 200px; margin-right: 10px;"
                @blur="validateIpSegment(item.ips, index)"
              ></el-input>
              <el-select 
                v-model="item.isp_code" 
                placeholder="请选择运营商"
                style="width: 180px; margin-right: 10px;"
              >
                <el-option
                  v-for="isp in ispList"
                  :key="isp.code"
                  :label="`${isp.cn_name} (${isp.en_name}, ${isp.code})`"
                  :value="isp.code">
                </el-option>
              </el-select>
              <el-input-number
                v-model="item.allow_out_province_ratio"
                placeholder="出省比例"
                :min="0"
                :max="100"
                :precision="0"
                style="width: 120px; margin-right: 10px;"
              ></el-input-number>
              <span style="margin-right: 10px; color: #909399; font-size: 12px;">%</span>
            </div>
            <div class="ip-row" style="margin-top: 10px;">
              <el-select 
                v-model="item.area" 
                placeholder="请选择大区"
                style="width: 180px; margin-right: 10px;"
                @change="handleAreaChange(item)"
              >
                <el-option
                  v-for="area in regionOptions"
                  :key="area.area_id"
                  :label="area.area_cnname"
                  :value="area.area_id">
                </el-option>
              </el-select>
              <el-select 
                v-model="item.province" 
                placeholder="请选择省份"
                style="width: 180px; margin-right: 10px;"
                @change="handleProvinceChange(item)"
              >
                <el-option
                  v-for="province in provinceOptions.filter(p => p.parent_id === item.area)"
                  :key="province.area_id"
                  :label="province.area_cnname"
                  :value="province.area_id">
                </el-option>
              </el-select>
              <el-select 
                v-model="item.city" 
                placeholder="请选择城市"
                style="width: 180px; margin-right: 10px;"
              >
                <el-option
                  v-for="city in cityOptions.filter(c => c.parent_id === item.province)"
                  :key="city.area_id"
                  :label="city.area_cnname"
                  :value="city.area_id">
                </el-option>
              </el-select>
              <el-button 
                type="danger" 
                size="small"
                circle
                icon="el-icon-minus"
                @click="removeIpItem(index)"
                :disabled="form.ips_list.length === 1"
              >
              </el-button>
              <el-button 
                type="success" 
                size="small" 
                circle
                @click="addIpItem"
                icon="el-icon-plus"
              >
              </el-button>
            </div>
            <div v-if="item.error" class="error-message">{{ item.error }}</div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from "../../http"
import { emailRegexp, pureNumberRegexp } from "@/utils/regexp"
import { mapState } from 'vuex'

export default {
  name: "supplierDialog",
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: true,
      loading: false,
      id: '',
      ispList: [], // 运营商列表
      form: {
        supplier_name: '',
        supplier_code: '',
        contact_group_id: '',
        contact_group_name: '',
        contact: '',
        email: '',
        phone: '',
        ips_list: [
          { ips: '', isp_code: '', allow_out_province_ratio: 0, area: '', province: '', city: '', error: '' }
        ]
      },
      rules: {
        supplier_name: [
          { required: true, message: '请输入供应商名称', trigger: 'blur' }
        ],
        supplier_code: [
          { required: true, message: '请输入供应商代码', trigger: 'blur' }
        ],
        contact_group_id: [
          { required: false, message: '请输入群ID', trigger: 'blur' }
        ],
        contact_group_name: [
          { required: false, message: '请输入群名称', trigger: 'blur' }
        ],
        contact: [
          { required: false, message: '请输入联系人', trigger: 'blur' }
        ],
        email: [
          { required: false, message: '请输入联系人邮箱', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && !emailRegexp.test(value)) {
                callback(new Error('请输入正确的邮箱格式'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        phone: [
          { required: false, message: '请输入联系人电话', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && !pureNumberRegexp.test(value)) {
                callback(new Error('请输入正确的电话号码（仅限数字）'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        ips_list: [
          { 
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('请至少添加一个IP段'))
              } else {
                const hasError = value.some(item => !item.ips || !item.isp_code || item.allow_out_province_ratio === null || item.allow_out_province_ratio === undefined || !item.area || !item.province || !item.city)
                if (hasError) {
                  callback(new Error('请完整填写IP段、运营商、出省比例、大区、省份、城市信息'))
                } else {
                  callback()
                }
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.getIspList()
  },
  computed: {
    ...mapState({
      areaList: state => state.baseData.areaList
    }),
    // 获取大区列表（level为2的数据）
    regionOptions() {
      return this.areaList.filter(item => item.level === '2')
    },
    // 获取省份列表（level为3的数据）
    provinceOptions() {
      return this.areaList.filter(item => item.level === '3')
    },
    // 获取城市列表（level为4的数据）
    cityOptions() {
      return this.areaList.filter(item => item.level === '4')
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.id = val.id
          this.form = {
            supplier_name: val.supplier_name || '',
            supplier_code: val.supplier_code || '',
            contact_group_id: val.contact_group_id || '',
            contact_group_name: val.contact_group_name || '',
            contact: val.contact || '',
            email: val.email || '',
            phone: val.phone || '',
            ips_list: this.parseIpsData(val.ips_list || val.ips)
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取运营商列表
    async getIspList() {
      try {
        const res = await http.getIspList()
        if (res && res.code === 100000) {
          this.ispList = res.data || []
        }
      } catch (error) {
        console.error('获取运营商列表失败:', error)
        this.$message.error('获取运营商列表失败')
      }
    },
    
    // 解析IP段数据，兼容旧格式
    parseIpsData(ipsData) {
      if (!ipsData) {
        return [{ ips: '', isp_code: '', allow_out_province_ratio: 0, area: '', province: '', city: '', error: '' }]
      }
      
      // 如果是新格式（数组）
      if (Array.isArray(ipsData)) {
        return ipsData.map(item => ({
          ips: item.ips || '',
          isp_code: item.isp_code || '',
          allow_out_province_ratio: item.allow_out_province_ratio || 0,
          area: item.area || '',
          province: item.province || '',
          city: item.city || '',
          error: ''
        }))
      }
      
      // 如果是旧格式（字符串）
      if (typeof ipsData === 'string' && ipsData.trim()) {
        const ipsArray = ipsData.split(',').map(ip => ip.trim()).filter(ip => ip)
        return ipsArray.map(ip => ({
          ips: ip,
          isp_code: '',
          allow_out_province_ratio: 0,
          area: '',
          province: '',
          city: '',
          error: ''
        }))
      }
      
      return [{ ips: '', isp_code: '', allow_out_province_ratio: 0, area: '', province: '', city: '', error: '' }]
    },
    
    // 添加IP段项
    addIpItem() {
      this.form.ips_list.push({ ips: '', isp_code: '', allow_out_province_ratio: 0, area: '', province: '', city: '', error: '' })
    },
    
    // 移除IP段项
    removeIpItem(index) {
      if (this.form.ips_list.length > 1) {
        this.form.ips_list.splice(index, 1)
      }
    },
    
    // 验证IP段格式
    validateIpSegment(ips, index) {
      if (!ips) {
        this.$set(this.form.ips_list[index], 'error', '')
        return
      }
      
      // 简单的IP段格式验证（支持CIDR格式）
      const ipSegmentRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/([0-9]|[1-2][0-9]|3[0-2]))?$/
      
      if (!ipSegmentRegex.test(ips)) {
        this.$set(this.form.ips_list[index], 'error', 'IP段格式不正确，请输入有效的IP地址或CIDR格式')
      } else {
        this.$set(this.form.ips_list[index], 'error', '')
      }
    },
    
    handleClose() {
      this.$refs.form.resetFields()
      this.$emit('close')
    },
    
    // 处理大区选择变化
    handleAreaChange(item) {
      // 当大区变化时，清空省份和城市的选择
      item.province = ''
      item.city = ''
    },
    
    // 处理省份选择变化
    handleProvinceChange(item) {
      // 当省份变化时，清空城市的选择
      item.city = ''
    },
    
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        
        // 验证IP段格式
        const hasInvalidIps = this.form.ips_list.some(item => {
          if (!item.ips || !item.isp_code || item.allow_out_province_ratio === null || item.allow_out_province_ratio === undefined || !item.area || !item.province || !item.city) return true
          this.validateIpSegment(item.ips, this.form.ips_list.indexOf(item))
          return item.error
        })
        
        if (hasInvalidIps) {
          this.$message.error('请检查IP段格式、运营商、出省比例、大区、省份和城市信息')
          return
        }
        
        this.loading = true
        const api = this.isEdit ? http.updateSupplier : http.addSupplier
        const params = {
          supplier_name: this.form.supplier_name,
          supplier_code: this.form.supplier_code,
          contact_group_id: this.form.contact_group_id,
          contact_group_name: this.form.contact_group_name,
          contact: this.form.contact,
          email: this.form.email,
          phone: this.form.phone,
          ips_list: this.form.ips_list.map(item => ({
            ips: item.ips,
            isp_code: item.isp_code,
            allow_out_province_ratio: item.allow_out_province_ratio,
            area: item.area,
            province: item.province,
            city: item.city
          }))
        }
        
        if (this.isEdit) {
          params.id = this.id
        }

        const res = await api(params)
        if (res && res.code === 100000) {
          this.$message.success(this.isEdit ? '修改成功' : '新增成功')
          this.dialogVisible = false
          this.$emit('refresh')
        }
      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.ip-section {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.ip-item {
  margin-bottom: 10px;
}

.ip-item:last-child {
  margin-bottom: 0;
}

.ip-row {
  display: flex;
  align-items: center;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 5px;
  margin-left: 5px;
}
</style>
